import { v4 as uuidv4 } from 'uuid';
import { NodeType, PortShape, PortColor, PortDirection } from '../types';
import type { GameNode, Port } from '../types';

// 随机选择端口形状
function getRandomPortShape(): PortShape {
  const shapes = Object.values(PortShape);
  return shapes[Math.floor(Math.random() * shapes.length)];
}

// 随机选择端口颜色
function getRandomPortColor(): PortColor {
  const colors = Object.values(PortColor);
  return colors[Math.floor(Math.random() * colors.length)];
}

// 创建端口
function createPort(
  nodeId: string,
  direction: PortDirection,
  shape: PortShape,
  color: PortColor,
  position: { x: number; y: number }
): Port {
  return {
    id: uuidv4(),
    shape,
    color,
    direction,
    nodeId,
    position,
    isConnected: false
  };
}

// 创建起点节点
export function createStartNode(position: { x: number; y: number }): GameNode {
  const nodeId = uuidv4();
  const outputPorts: Port[] = [];
  
  // 起点节点有1-3个输出端口
  const portCount = Math.floor(Math.random() * 3) + 1;
  
  for (let i = 0; i < portCount; i++) {
    const portPosition = {
      x: position.x + 120, // 节点右侧
      y: position.y + 20 + i * 30
    };
    
    outputPorts.push(createPort(
      nodeId,
      PortDirection.OUTPUT,
      getRandomPortShape(),
      getRandomPortColor(),
      portPosition
    ));
  }
  
  return {
    id: nodeId,
    type: NodeType.START,
    position,
    inputPorts: [],
    outputPorts
  };
}

// 创建终点节点
export function createEndNode(position: { x: number; y: number }): GameNode {
  const nodeId = uuidv4();
  const inputPorts: Port[] = [];
  
  // 终点节点有1-3个输入端口
  const portCount = Math.floor(Math.random() * 3) + 1;
  
  for (let i = 0; i < portCount; i++) {
    const portPosition = {
      x: position.x, // 节点左侧
      y: position.y + 20 + i * 30
    };
    
    inputPorts.push(createPort(
      nodeId,
      PortDirection.INPUT,
      getRandomPortShape(),
      getRandomPortColor(),
      portPosition
    ));
  }
  
  return {
    id: nodeId,
    type: NodeType.END,
    position,
    inputPorts,
    outputPorts: []
  };
}

// 创建处理节点
export function createProcessNode(position: { x: number; y: number }): GameNode {
  const nodeId = uuidv4();
  const inputPorts: Port[] = [];
  const outputPorts: Port[] = [];
  
  // 处理节点有1-2个输入端口和1-2个输出端口
  const inputPortCount = Math.floor(Math.random() * 2) + 1;
  const outputPortCount = Math.floor(Math.random() * 2) + 1;
  
  // 创建输入端口
  for (let i = 0; i < inputPortCount; i++) {
    const portPosition = {
      x: position.x, // 节点左侧
      y: position.y + 20 + i * 30
    };
    
    inputPorts.push(createPort(
      nodeId,
      PortDirection.INPUT,
      getRandomPortShape(),
      getRandomPortColor(),
      portPosition
    ));
  }
  
  // 创建输出端口
  for (let i = 0; i < outputPortCount; i++) {
    const portPosition = {
      x: position.x + 120, // 节点右侧
      y: position.y + 20 + i * 30
    };
    
    outputPorts.push(createPort(
      nodeId,
      PortDirection.OUTPUT,
      getRandomPortShape(),
      getRandomPortColor(),
      portPosition
    ));
  }
  
  return {
    id: nodeId,
    type: NodeType.PROCESS,
    position,
    inputPorts,
    outputPorts
  };
}

// 生成兼容的节点（确保可以与现有节点连接）
export function generateCompatibleNode(existingNodes: GameNode[]): GameNode {
  const position = { x: 0, y: 0 }; // 临时位置，稍后会被设置

  // 收集所有未连接的端口
  const unconnectedPorts: Port[] = [];
  existingNodes.forEach(node => {
    node.inputPorts.forEach(port => {
      if (!port.isConnected) unconnectedPorts.push(port);
    });
    node.outputPorts.forEach(port => {
      if (!port.isConnected) unconnectedPorts.push(port);
    });
  });

  if (unconnectedPorts.length === 0) {
    // 如果没有未连接的端口，生成随机节点
    return createProcessNode(position);
  }

  // 随机选择一个未连接的端口
  const targetPort = unconnectedPorts[Math.floor(Math.random() * unconnectedPorts.length)];

  // 生成一个可以与目标端口连接的节点
  const nodeId = uuidv4();
  const inputPorts: Port[] = [];
  const outputPorts: Port[] = [];

  if (targetPort.direction === PortDirection.OUTPUT) {
    // 目标是输出端口，我们需要创建一个有匹配输入端口的节点
    const matchingInputPort = createPort(
      nodeId,
      PortDirection.INPUT,
      targetPort.shape,
      targetPort.color,
      { x: position.x, y: position.y + 20 }
    );
    inputPorts.push(matchingInputPort);

    // 添加一些随机输出端口
    const outputPortCount = Math.floor(Math.random() * 2) + 1;
    for (let i = 0; i < outputPortCount; i++) {
      outputPorts.push(createPort(
        nodeId,
        PortDirection.OUTPUT,
        getRandomPortShape(),
        getRandomPortColor(),
        { x: position.x + 120, y: position.y + 20 + i * 30 }
      ));
    }
  } else {
    // 目标是输入端口，我们需要创建一个有匹配输出端口的节点
    const matchingOutputPort = createPort(
      nodeId,
      PortDirection.OUTPUT,
      targetPort.shape,
      targetPort.color,
      { x: position.x + 120, y: position.y + 20 }
    );
    outputPorts.push(matchingOutputPort);

    // 添加一些随机输入端口
    const inputPortCount = Math.floor(Math.random() * 2) + 1;
    for (let i = 0; i < inputPortCount; i++) {
      inputPorts.push(createPort(
        nodeId,
        PortDirection.INPUT,
        getRandomPortShape(),
        getRandomPortColor(),
        { x: position.x, y: position.y + 20 + i * 30 }
      ));
    }
  }

  return {
    id: nodeId,
    type: NodeType.PROCESS,
    position,
    inputPorts,
    outputPorts
  };
}

// 智能节点池生成器 - 确保生成的节点池可解
export function generateSolvableNodePool(
  startNodes: GameNode[],
  endNodes: GameNode[],
  poolSize: number = 5
): GameNode[] {
  const nodePool: GameNode[] = [];
  const maxAttempts = 100;

  for (let i = 0; i < poolSize; i++) {
    let attempts = 0;
    let validNode: GameNode | null = null;

    while (attempts < maxAttempts && !validNode) {
      // 生成候选节点
      const candidateNode = generateSmartNode(startNodes, endNodes, nodePool);

      // 验证添加此节点后是否仍然可解
      if (isNodePoolSolvable([...startNodes, ...endNodes, ...nodePool, candidateNode])) {
        validNode = candidateNode;
      }
      attempts++;
    }

    if (validNode) {
      nodePool.push(validNode);
    } else {
      // 如果无法生成有效节点，生成一个简单的桥接节点
      nodePool.push(generateBridgeNode(startNodes, endNodes));
    }
  }

  return nodePool;
}

// 生成智能节点 - 基于现有节点的端口需求
function generateSmartNode(
  startNodes: GameNode[],
  endNodes: GameNode[],
  existingPool: GameNode[]
): GameNode {
  const position = { x: 0, y: 0 };
  const nodeId = uuidv4();

  // 收集所有需要连接的端口类型
  const allNodes = [...startNodes, ...endNodes, ...existingPool];
  const unconnectedOutputs = allNodes.flatMap(node =>
    node.outputPorts.filter(port => !port.isConnected)
  );
  const unconnectedInputs = allNodes.flatMap(node =>
    node.inputPorts.filter(port => !port.isConnected)
  );

  const inputPorts: Port[] = [];
  const outputPorts: Port[] = [];

  // 策略1: 创建桥接节点（连接现有的输出到输入）
  if (unconnectedOutputs.length > 0 && unconnectedInputs.length > 0) {
    const outputPort = unconnectedOutputs[Math.floor(Math.random() * unconnectedOutputs.length)];
    const inputPort = unconnectedInputs[Math.floor(Math.random() * unconnectedInputs.length)];

    // 创建匹配的输入端口
    inputPorts.push(createPort(
      nodeId,
      PortDirection.INPUT,
      outputPort.shape,
      outputPort.color,
      { x: position.x, y: position.y + 20 }
    ));

    // 创建匹配的输出端口
    outputPorts.push(createPort(
      nodeId,
      PortDirection.OUTPUT,
      inputPort.shape,
      inputPort.color,
      { x: position.x + 120, y: position.y + 20 }
    ));
  }

  // 添加一些随机端口以增加复杂性
  const extraInputs = Math.floor(Math.random() * 2);
  const extraOutputs = Math.floor(Math.random() * 2);

  for (let i = 0; i < extraInputs; i++) {
    inputPorts.push(createPort(
      nodeId,
      PortDirection.INPUT,
      getRandomPortShape(),
      getRandomPortColor(),
      { x: position.x, y: position.y + 20 + (inputPorts.length + i) * 30 }
    ));
  }

  for (let i = 0; i < extraOutputs; i++) {
    outputPorts.push(createPort(
      nodeId,
      PortDirection.OUTPUT,
      getRandomPortShape(),
      getRandomPortColor(),
      { x: position.x + 120, y: position.y + 20 + (outputPorts.length + i) * 30 }
    ));
  }

  return {
    id: nodeId,
    type: NodeType.PROCESS,
    position,
    inputPorts,
    outputPorts
  };
}

// 生成桥接节点 - 简单的输入到输出转换
function generateBridgeNode(startNodes: GameNode[], endNodes: GameNode[]): GameNode {
  const position = { x: 0, y: 0 };
  const nodeId = uuidv4();

  // 从起点节点选择一个输出端口类型
  const startOutputs = startNodes.flatMap(node => node.outputPorts);
  const endInputs = endNodes.flatMap(node => node.inputPorts);

  const inputPorts: Port[] = [];
  const outputPorts: Port[] = [];

  if (startOutputs.length > 0) {
    const sourcePort = startOutputs[Math.floor(Math.random() * startOutputs.length)];
    inputPorts.push(createPort(
      nodeId,
      PortDirection.INPUT,
      sourcePort.shape,
      sourcePort.color,
      { x: position.x, y: position.y + 20 }
    ));
  }

  if (endInputs.length > 0) {
    const targetPort = endInputs[Math.floor(Math.random() * endInputs.length)];
    outputPorts.push(createPort(
      nodeId,
      PortDirection.OUTPUT,
      targetPort.shape,
      targetPort.color,
      { x: position.x + 120, y: position.y + 20 }
    ));
  }

  return {
    id: nodeId,
    type: NodeType.PROCESS,
    position,
    inputPorts,
    outputPorts
  };
}

// 检查节点池是否可解
function isNodePoolSolvable(nodes: GameNode[]): boolean {
  // 简化的可解性检查
  const startNodes = nodes.filter(node => node.type === NodeType.START);
  const endNodes = nodes.filter(node => node.type === NodeType.END);

  if (startNodes.length === 0 || endNodes.length === 0) {
    return false;
  }

  // 检查是否有足够的端口类型匹配
  const outputPorts = nodes.flatMap(node => node.outputPorts);
  const inputPorts = nodes.flatMap(node => node.inputPorts);

  // 为每种端口类型计算数量
  const outputCounts = new Map<string, number>();
  const inputCounts = new Map<string, number>();

  outputPorts.forEach(port => {
    const key = `${port.shape}-${port.color}`;
    outputCounts.set(key, (outputCounts.get(key) || 0) + 1);
  });

  inputPorts.forEach(port => {
    const key = `${port.shape}-${port.color}`;
    inputCounts.set(key, (inputCounts.get(key) || 0) + 1);
  });

  // 检查每种输入端口类型是否有对应的输出端口
  for (const [key, inputCount] of inputCounts) {
    const outputCount = outputCounts.get(key) || 0;
    if (outputCount < inputCount) {
      return false;
    }
  }

  return true;
}
