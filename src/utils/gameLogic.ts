import { NodeType } from '../types';
import type { GameNode, Connection, Port } from '../types';

// 检查两个端口是否可以连接
export function canConnectPorts(fromPort: Port, toPort: Port): boolean {
  // 必须是不同方向的端口
  if (fromPort.direction === toPort.direction) {
    return false;
  }
  
  // 必须是相同形状和颜色
  if (fromPort.shape !== toPort.shape || fromPort.color !== toPort.color) {
    return false;
  }
  
  // 不能连接到同一个节点
  if (fromPort.nodeId === toPort.nodeId) {
    return false;
  }
  
  // 端口不能已经被连接
  if (fromPort.isConnected || toPort.isConnected) {
    return false;
  }
  
  return true;
}

// 检查图是否有环（有向无环图检查）
export function hasCircularDependency(nodes: GameNode[], connections: Connection[]): boolean {
  const visited = new Set<string>();
  const recursionStack = new Set<string>();
  
  // 构建邻接表
  const adjacencyList = new Map<string, string[]>();
  nodes.forEach(node => {
    adjacencyList.set(node.id, []);
  });
  
  connections.forEach(connection => {
    const fromNodeConnections = adjacencyList.get(connection.fromNodeId) || [];
    fromNodeConnections.push(connection.toNodeId);
    adjacencyList.set(connection.fromNodeId, fromNodeConnections);
  });
  
  // DFS检查环
  function dfs(nodeId: string): boolean {
    visited.add(nodeId);
    recursionStack.add(nodeId);
    
    const neighbors = adjacencyList.get(nodeId) || [];
    for (const neighbor of neighbors) {
      if (!visited.has(neighbor)) {
        if (dfs(neighbor)) {
          return true;
        }
      } else if (recursionStack.has(neighbor)) {
        return true; // 发现环
      }
    }
    
    recursionStack.delete(nodeId);
    return false;
  }
  
  // 检查所有节点
  for (const node of nodes) {
    if (!visited.has(node.id)) {
      if (dfs(node.id)) {
        return true;
      }
    }
  }
  
  return false;
}

// 计算节点深度（拓扑排序）
export function calculateNodeDepths(nodes: GameNode[], connections: Connection[]): Map<string, number> {
  const depths = new Map<string, number>();
  const inDegree = new Map<string, number>();
  const adjacencyList = new Map<string, string[]>();
  
  // 初始化
  nodes.forEach(node => {
    inDegree.set(node.id, 0);
    adjacencyList.set(node.id, []);
    if (node.type === NodeType.START) {
      depths.set(node.id, 0);
    }
  });
  
  // 构建图和计算入度
  connections.forEach(connection => {
    const fromConnections = adjacencyList.get(connection.fromNodeId) || [];
    fromConnections.push(connection.toNodeId);
    adjacencyList.set(connection.fromNodeId, fromConnections);
    
    const currentInDegree = inDegree.get(connection.toNodeId) || 0;
    inDegree.set(connection.toNodeId, currentInDegree + 1);
  });
  
  // 拓扑排序
  const queue: string[] = [];
  nodes.forEach(node => {
    if (inDegree.get(node.id) === 0) {
      queue.push(node.id);
      if (!depths.has(node.id)) {
        depths.set(node.id, 0);
      }
    }
  });
  
  while (queue.length > 0) {
    const currentNodeId = queue.shift()!;
    const currentDepth = depths.get(currentNodeId) || 0;
    
    const neighbors = adjacencyList.get(currentNodeId) || [];
    neighbors.forEach(neighborId => {
      const newInDegree = (inDegree.get(neighborId) || 0) - 1;
      inDegree.set(neighborId, newInDegree);
      
      const newDepth = currentDepth + 1;
      const existingDepth = depths.get(neighborId) || 0;
      depths.set(neighborId, Math.max(existingDepth, newDepth));
      
      if (newInDegree === 0) {
        queue.push(neighborId);
      }
    });
  }
  
  return depths;
}

// 检查游戏是否可解
export function isGameSolvable(nodes: GameNode[], connections: Connection[]): boolean {
  // 检查是否有环
  if (hasCircularDependency(nodes, connections)) {
    return false;
  }
  
  // 检查是否有起点和终点
  const hasStart = nodes.some(node => node.type === NodeType.START);
  const hasEnd = nodes.some(node => node.type === NodeType.END);
  
  if (!hasStart || !hasEnd) {
    return false;
  }
  
  // 检查所有节点是否都能连通
  const depths = calculateNodeDepths(nodes, connections);
  const endNodes = nodes.filter(node => node.type === NodeType.END);
  
  // 所有终点节点都应该有深度值（可达）
  return endNodes.every(endNode => depths.has(endNode.id));
}

// 检查所有端口是否都已连接
export function areAllPortsConnected(nodes: GameNode[]): boolean {
  for (const node of nodes) {
    // 起点节点的输入端口可以不连接
    if (node.type !== NodeType.START) {
      if (node.inputPorts.some(port => !port.isConnected)) {
        return false;
      }
    }

    // 终点节点的输出端口可以不连接
    if (node.type !== NodeType.END) {
      if (node.outputPorts.some(port => !port.isConnected)) {
        return false;
      }
    }
  }

  return true;
}

// 计算游戏得分
export function calculateScore(
  nodes: GameNode[],
  connections: Connection[],
  level: number,
  timeBonus: number = 0
): number {
  let score = 0;

  // 基础分数：每个连接10分
  score += connections.length * 10;

  // 节点复杂度奖励：每个处理节点5分
  const processNodes = nodes.filter(node => node.type === NodeType.PROCESS);
  score += processNodes.length * 5;

  // 端口多样性奖励
  const uniquePortTypes = new Set();
  nodes.forEach(node => {
    [...node.inputPorts, ...node.outputPorts].forEach(port => {
      uniquePortTypes.add(`${port.shape}-${port.color}`);
    });
  });
  score += uniquePortTypes.size * 3;

  // 等级乘数
  score *= level;

  // 时间奖励
  score += timeBonus;

  return Math.floor(score);
}

// 检查游戏完成条件
export function checkGameCompletion(nodes: GameNode[], connections: Connection[]): {
  isComplete: boolean;
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // 检查是否有环
  if (hasCircularDependency(nodes, connections)) {
    errors.push('检测到循环依赖');
  }

  // 检查所有必要端口是否连接
  if (!areAllPortsConnected(nodes)) {
    errors.push('存在未连接的端口');
  }

  // 检查是否可解
  if (!isGameSolvable(nodes, connections)) {
    errors.push('当前配置无法解决');
  }

  const isValid = errors.length === 0;
  const isComplete = isValid && areAllPortsConnected(nodes);

  return {
    isComplete,
    isValid,
    errors
  };
}

// 生成下一关的配置
export function generateNextLevel(currentLevel: number): {
  startNodeCount: number;
  endNodeCount: number;
  maxTempNodes: number;
  timeLimit?: number;
} {
  const baseConfig = {
    startNodeCount: 1,
    endNodeCount: 1,
    maxTempNodes: 3,
    timeLimit: undefined as number | undefined
  };

  // 根据等级调整难度
  if (currentLevel >= 3) {
    baseConfig.startNodeCount = Math.min(2, Math.floor(currentLevel / 3));
  }

  if (currentLevel >= 5) {
    baseConfig.endNodeCount = Math.min(2, Math.floor(currentLevel / 5));
  }

  if (currentLevel >= 2) {
    baseConfig.maxTempNodes = Math.min(6, 3 + Math.floor(currentLevel / 2));
  }

  // 从第10关开始添加时间限制
  if (currentLevel >= 10) {
    baseConfig.timeLimit = Math.max(60, 180 - currentLevel * 5);
  }

  return baseConfig;
}

// 俄罗斯方块模式的节点掉落逻辑
export function shouldDropNewNode(
  currentNodes: number,
  maxNodes: number,
  dropInterval: number,
  lastDropTime: number
): boolean {
  const now = Date.now();
  return (
    currentNodes < maxNodes &&
    (now - lastDropTime) >= dropInterval
  );
}

// 计算俄罗斯方块模式的掉落间隔
export function calculateDropInterval(level: number): number {
  // 基础间隔5秒，每级减少200ms，最小1秒
  return Math.max(1000, 5000 - (level - 1) * 200);
}
