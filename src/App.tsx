import { useState, useCallback, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { GameMode } from './types';
import type {
  GameState,
  GameNode,
  Connection as GameConnection
} from './types';
import {
  isGameSolvable,
  areAllPortsConnected,
  calculateNodeDepths
} from './utils/gameLogic';
import {
  createStartNode,
  createEndNode,
  generateCompatibleNode
} from './utils/nodeGenerator';
import GameCanvas from './components/GameCanvas';
import TemporaryNodeArea from './components/TemporaryNodeArea';
import GameControls from './components/GameControls';
import './App.css';

function App() {
  const [gameState, setGameState] = useState<GameState>({
    nodes: [],
    connections: [],
    temporaryNodes: [],
    isGameRunning: false,
    score: 0,
    level: 1
  });

  const [gameMode, setGameMode] = useState<GameMode>(GameMode.TURN_BASED);
  const [selectedTempNodeId, setSelectedTempNodeId] = useState<string | null>(null);
  const [isAnimationPlaying, setIsAnimationPlaying] = useState(false);

  // 初始化游戏
  const initializeGame = useCallback(() => {
    const startNode = createStartNode({ x: 50, y: 200 });
    const endNode = createEndNode({ x: 700, y: 200 });

    // 生成一些初始的临时节点
    const initialTempNodes = [
      generateCompatibleNode([startNode, endNode]),
      generateCompatibleNode([startNode, endNode])
    ];

    setGameState({
      nodes: [startNode, endNode],
      connections: [],
      temporaryNodes: initialTempNodes,
      isGameRunning: false,
      score: 0,
      level: 1
    });
    setSelectedTempNodeId(null);
  }, []);

  // 初始化游戏
  useEffect(() => {
    initializeGame();
  }, [initializeGame]);

  // 生成新节点
  const generateNewNode = useCallback(() => {
    if (gameState.temporaryNodes.length >= 3) return;

    const newNode = generateCompatibleNode(gameState.nodes);
    setGameState(prev => ({
      ...prev,
      temporaryNodes: [...prev.temporaryNodes, newNode]
    }));
  }, [gameState.nodes, gameState.temporaryNodes.length]);

  // 移动节点
  const handleNodeMove = useCallback((nodeId: string, newPosition: { x: number; y: number }) => {
    setGameState(prev => ({
      ...prev,
      nodes: prev.nodes.map(node =>
        node.id === nodeId
          ? {
              ...node,
              position: newPosition,
              inputPorts: node.inputPorts.map(port => ({
                ...port,
                position: { x: 0, y: port.position.y }
              })),
              outputPorts: node.outputPorts.map(port => ({
                ...port,
                position: { x: 120, y: port.position.y }
              }))
            }
          : node
      )
    }));
  }, []);

  // 创建连接
  const handleConnectionCreate = useCallback((fromPortId: string, toPortId: string) => {
    const fromPort = gameState.nodes
      .flatMap(node => [...node.inputPorts, ...node.outputPorts])
      .find(port => port.id === fromPortId);

    const toPort = gameState.nodes
      .flatMap(node => [...node.inputPorts, ...node.outputPorts])
      .find(port => port.id === toPortId);

    if (!fromPort || !toPort) return;

    const newConnection: GameConnection = {
      id: uuidv4(),
      fromPortId,
      toPortId,
      fromNodeId: fromPort.nodeId,
      toNodeId: toPort.nodeId
    };

    setGameState(prev => ({
      ...prev,
      connections: [...prev.connections, newConnection],
      nodes: prev.nodes.map(node => ({
        ...node,
        inputPorts: node.inputPorts.map(port =>
          port.id === fromPortId || port.id === toPortId
            ? { ...port, isConnected: true }
            : port
        ),
        outputPorts: node.outputPorts.map(port =>
          port.id === fromPortId || port.id === toPortId
            ? { ...port, isConnected: true }
            : port
        )
      }))
    }));
  }, [gameState.nodes]);

  // 删除连接
  const handleConnectionDelete = useCallback((connectionId: string) => {
    const connection = gameState.connections.find(c => c.id === connectionId);
    if (!connection) return;

    setGameState(prev => ({
      ...prev,
      connections: prev.connections.filter(c => c.id !== connectionId),
      nodes: prev.nodes.map(node => ({
        ...node,
        inputPorts: node.inputPorts.map(port =>
          port.id === connection.fromPortId || port.id === connection.toPortId
            ? { ...port, isConnected: false }
            : port
        ),
        outputPorts: node.outputPorts.map(port =>
          port.id === connection.fromPortId || port.id === connection.toPortId
            ? { ...port, isConnected: false }
            : port
        )
      }))
    }));
  }, [gameState.connections]);

  // 从临时区域添加节点到游戏区域
  const handleAddNodeFromTemp = useCallback((tempNode: GameNode, position: { x: number; y: number }) => {
    const newNode: GameNode = {
      ...tempNode,
      position,
      inputPorts: tempNode.inputPorts.map(port => ({
        ...port,
        position: { x: 0, y: port.position.y }
      })),
      outputPorts: tempNode.outputPorts.map(port => ({
        ...port,
        position: { x: 120, y: port.position.y }
      }))
    };

    setGameState(prev => ({
      ...prev,
      nodes: [...prev.nodes, newNode],
      temporaryNodes: prev.temporaryNodes.filter(node => node.id !== tempNode.id)
    }));
    setSelectedTempNodeId(null);
  }, []);

  return (
    <div className="app">
      <div className="game-layout">
        <div className="left-panel">
          <GameControls
            gameState={gameState}
            gameMode={gameMode}
            onGameModeChange={setGameMode}
            onStartGame={() => setGameState(prev => ({ ...prev, isGameRunning: true }))}
            onPauseGame={() => setGameState(prev => ({ ...prev, isGameRunning: false }))}
            onResetGame={initializeGame}
            onGenerateNode={generateNewNode}
            onTestSolution={() => {
              const depths = calculateNodeDepths(gameState.nodes, gameState.connections);
              setGameState(prev => ({
                ...prev,
                nodes: prev.nodes.map(node => ({
                  ...node,
                  depth: depths.get(node.id)
                }))
              }));
              setIsAnimationPlaying(true);
            }}
            canGenerateNode={gameState.temporaryNodes.length < 3}
            isSolvable={isGameSolvable(gameState.nodes, gameState.connections)}
            allPortsConnected={areAllPortsConnected(gameState.nodes)}
          />
        </div>

        <div className="center-panel">
          <GameCanvas
            nodes={gameState.nodes}
            connections={gameState.connections}
            onNodeMove={handleNodeMove}
            onConnectionCreate={handleConnectionCreate}
            onConnectionDelete={handleConnectionDelete}
            onNodeAdd={handleAddNodeFromTemp}
            selectedTempNode={gameState.temporaryNodes.find(node => node.id === selectedTempNodeId) || null}
            isAnimationPlaying={isAnimationPlaying}
            onAnimationComplete={() => setIsAnimationPlaying(false)}
            width={800}
            height={600}
          />
        </div>

        <div className="right-panel">
          <TemporaryNodeArea
            temporaryNodes={gameState.temporaryNodes}
            onNodeSelect={(node) => setSelectedTempNodeId(node.id)}
            selectedNodeId={selectedTempNodeId}
          />
        </div>
      </div>
    </div>
  );
}

export default App;
